<execution>
  <constraint>
    ## 设计过程客观限制
    - **时间约束**：设计项目通常有严格的时间期限和里程碑
    - **资源限制**：团队规模、预算和可用工具会限制设计可能性
    - **技术约束**：目标平台的技术能力和限制会影响设计决策
    - **品牌规范**：必须遵循既定的品牌标识和设计语言
    - **可访问性要求**：设计必须符合WCAG等可访问性标准
    - **多设备适配**：设计必须在不同屏幕尺寸和设备上正常工作
    - **性能要求**：设计实现不得影响应用的加载速度和响应性
  </constraint>
  
  <rule>
    ## 设计过程强制规则
    - **用户研究必要性**：任何重要设计决策必须基于用户研究数据
    - **设计评审流程**：所有关键设计必须经过正式的设计评审
    - **原型验证要求**：重要功能必须通过可交互原型进行用户测试
    - **设计文档标准**：必须创建符合团队标准的设计规范文档
    - **可访问性合规**：设计必须满足AA级WCAG标准的所有要求
    - **响应式设计原则**：所有界面必须采用响应式设计方法
    - **设计系统一致性**：新设计元素必须与现有设计系统保持一致
    - **版本控制规范**：设计文件必须使用规范的版本控制方法管理
  </rule>
  
  <guideline>
    ## 设计过程指导原则
    - **简约至上**：追求最简化的设计解决方案，避免不必要的复杂性
    - **渐进式设计**：从核心功能开始，逐步增加复杂度和细节
    - **数据驱动决策**：优先考虑基于数据和测试结果的设计决策
    - **设计模式复用**：优先使用经过验证的设计模式和组件
    - **早期反馈循环**：尽早获取用户和团队反馈，缩短迭代周期
    - **协作设计**：鼓励跨职能团队成员参与设计过程
    - **设计债务管理**：识别并计划解决积累的设计不一致和问题
    - **持续学习**：不断更新设计知识和技能，跟踪行业趋势
  </guideline>
  
  <process>
    ## UI/UX设计执行流程
    
    ### Phase 1: 研究与发现
    
    #### 1.1 项目启动
    1. **项目简报分析**
       - 审阅项目文档和需求
       - 确认项目目标和成功指标
       - 识别关键利益相关者
    
    2. **范围确定**
       - 定义设计工作的边界
       - 确认设计交付物清单
       - 制定初步时间表
    
    3. **背景研究**
       - 了解业务目标和约束
       - 收集行业背景信息
       - 分析现有产品数据
    
    #### 1.2 用户研究
    1. **研究计划制定**
       - 选择适当的研究方法
       - 确定研究问题和目标
       - 制定参与者招募标准
    
    2. **研究执行**
       - 进行用户访谈和观察
       - 执行问卷调查
       - 分析使用数据和反馈
    
    3. **竞品分析**
       - 识别主要竞争对手
       - 评估竞品优缺点
       - 提取可借鉴的设计模式
    
    #### 1.3 研究分析与综合
    1. **数据整理**
       - 组织原始研究数据
       - 识别关键模式和主题
       - 提炼用户洞察
    
    2. **用户画像创建**
       - 构建代表性用户画像
       - 定义用户目标和动机
       - 描述用户行为模式
    
    3. **用户旅程映射**
       - 绘制现有用户旅程
       - 识别痛点和机会
       - 确定优化重点
    
    4. **需求定义**
       - 转化用户洞察为设计需求
       - 按优先级排序需求
       - 创建设计简报文档
    
    ### Phase 2: 构思与设计
    
    #### 2.1 概念设计
    1. **头脑风暴**
       - 组织设计工作坊
       - 生成多种设计概念
       - 探索不同解决方案
    
    2. **信息架构设计**
       - 创建内容清单
       - 设计信息层次结构
       - 规划导航系统
    
    3. **用户流程设计**
       - 绘制关键任务流程
       - 定义理想用户旅程
       - 设计交互模型
    
    #### 2.2 界面设计
    1. **线框图设计**
       - 创建低保真线框图
       - 定义页面布局和结构
       - 规划内容组织
    
    2. **设计系统规划**
       - 定义色彩系统
       - 选择排版方案
       - 设计基础UI组件
    
    3. **视觉设计探索**
       - 创建设计风格探索
       - 应用品牌元素
       - 开发视觉语言
    
    4. **高保真设计**
       - 创建详细界面设计
       - 定义组件状态和变体
       - 应用视觉风格指南
    
    5. **响应式设计**
       - 定义断点策略
       - 调整各屏幕尺寸布局
       - 确保跨设备一致体验
    
    ### Phase 3: 原型与测试
    
    #### 3.1 原型制作
    1. **交互原型创建**
       - 构建可点击原型
       - 模拟关键交互和转场
       - 准备测试场景
    
    2. **内部评审**
       - 组织设计评审会议
       - 收集团队反馈
       - 进行可用性启发式评估
    
    #### 3.2 用户测试
    1. **测试计划制定**
       - 设计测试任务和场景
       - 确定评估指标
       - 招募测试参与者
    
    2. **测试执行**
       - 引导用户完成测试任务
       - 记录用户行为和反馈
       - 观察问题和困难点
    
    3. **测试分析**
       - 整理测试数据
       - 识别关键问题和模式
       - 提出改进建议
    
    #### 3.3 设计迭代
    1. **设计优化**
       - 基于测试结果修改设计
       - 解决发现的问题
       - 改进用户体验
    
    2. **验证测试**
       - 测试优化后的设计
       - 确认问题是否解决
       - 评估整体体验改进
    
    ### Phase 4: 实施与优化
    
    #### 4.1 设计交付
    1. **设计规范文档**
       - 创建详细的设计系统文档
       - 定义组件规格和用法
       - 编写交互指南
    
    2. **资产准备**
       - 导出设计资产
       - 优化图像和图标
       - 准备动效规范
    
    3. **开发交接**
       - 与开发团队会议
       - 解释设计意图和细节
       - 回答技术实现问题
    
    #### 4.2 实施支持
    1. **开发协作**
       - 参与开发冲刺计划
       - 提供设计咨询
       - 解决实现过程中的问题
    
    2. **质量保证**
       - 审查实现的界面
       - 验证与设计的一致性
       - 确认交互细节
    
    #### 4.3 持续优化
    1. **发布后评估**
       - 收集用户反馈
       - 分析使用数据
       - 评估设计成效
    
    2. **迭代规划**
       - 识别改进机会
       - 优先排序优化项目
       - 规划后续迭代
    
    ```mermaid
    flowchart TD
      A[研究与发现] --> B[构思与设计]
      B --> C[原型与测试]
      C --> D[实施与优化]
      D --> E{评估}
      E -->|需要改进| C
      E -->|符合要求| F[项目完成]
      
      A --> A1[用户研究]
      A --> A2[竞品分析]
      A --> A3[需求定义]
      
      B --> B1[信息架构]
      B --> B2[线框图]
      B --> B3[视觉设计]
      
      C --> C1[原型制作]
      C --> C2[用户测试]
      C --> C3[设计迭代]
      
      D --> D1[设计规范]
      D --> D2[开发支持]
      D --> D3[数据分析]
    ```
  </process>
  
  <criteria>
    ## 设计质量评价标准
    
    ### 用户体验质量
    - ✅ **任务完成效率**：用户能否高效完成核心任务
      - 任务完成时间低于基准值
      - 任务完成率达到95%以上
      - 用户操作步骤最小化
    
    - ✅ **学习曲线**：新用户能否轻松上手
      - 无需指导即可完成基本任务
      - 界面元素自解释性强
      - 提供适当的引导和提示
    
    - ✅ **错误处理**：系统如何预防和处理错误
      - 预防常见用户错误
      - 错误信息清晰有帮助
      - 提供简单的恢复机制
    
    - ✅ **用户满意度**：用户对体验的主观评价
      - SUS评分达到80分以上
      - NPS评分达到40以上
      - 用户反馈积极正面
    
    ### 视觉设计质量
    - ✅ **品牌一致性**：设计是否符合品牌形象
      - 正确使用品牌色彩和标识
      - 视觉风格符合品牌气质
      - 传达一致的品牌信息
    
    - ✅ **视觉层次**：信息组织是否清晰有序
      - 重要内容视觉突出
      - 内容分组逻辑合理
      - 视觉引导自然流畅
    
    - ✅ **美学质量**：设计的视觉吸引力
      - 色彩搭配和谐
      - 排版规范专业
      - 视觉元素精致统一
    
    - ✅ **细节完成度**：设计细节的精致程度
      - 组件状态完整定义
      - 边缘情况得到处理
      - 微交互设计精细
    
    ### 技术实现质量
    - ✅ **响应式适配**：跨设备体验一致性
      - 在所有目标设备上正常显示
      - 交互元素适应不同输入方式
      - 内容布局智能调整
    
    - ✅ **可访问性合规**：符合可访问性标准
      - 通过WCAG AA级检查
      - 支持屏幕阅读器
      - 提供键盘导航支持
    
    - ✅ **性能优化**：设计对性能的影响
      - 图像和资源优化
      - 动画效果流畅不卡顿
      - 加载策略合理
    
    - ✅ **代码实现质量**：设计到代码转换的准确性
      - 实现与设计高度一致
      - CSS结构清晰高效
      - 组件复用率高
    
    ### 业务目标达成
    - ✅ **转化率提升**：设计对关键转化的影响
      - 目标转化率提升达到预期
      - 漏斗流失率降低
      - 用户参与度提高
    
    - ✅ **效率提升**：设计对用户效率的影响
      - 任务完成时间减少
      - 操作错误率降低
      - 用户满意度提升
    
    - ✅ **成本效益**：设计投资回报
      - 开发实现成本合理
      - 维护成本可控
      - 设计可扩展性好
  </criteria>
</execution> 