<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书智能体 - 全页面原型展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            padding: 20px;
        }

        .prototype-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-title {
            text-align: center;
            color: white;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 40px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .phone-frame {
            width: 380px;
            height: 680px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            position: relative;
        }

        .phone-header {
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 0;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 12px;
            color: #666;
        }

        .page-content {
            padding: 20px;
            height: calc(100% - 40px);
            overflow-y: auto;
        }

        .page-label {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
        }

        .back-btn {
            background: rgba(116, 75, 162, 0.1);
            border: none;
            padding: 8px 12px;
            border-radius: 8px;
            color: #764ba2;
            font-size: 14px;
        }

        .card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .input-group {
            margin-bottom: 16px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
        }

        .input-group input,
        .input-group select,
        .input-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            margin-top: 16px;
        }

        .btn-secondary {
            background: rgba(116, 75, 162, 0.1);
            color: #764ba2;
            border: 1px solid rgba(116, 75, 162, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            margin: 5px;
        }

        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .nav-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .grid-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 16px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 14px;
        }

        .member-badge {
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            color: #333;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        .points-display {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            display: flex;
            padding: 12px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #666;
            font-size: 12px;
        }

        .nav-item.active {
            color: #764ba2;
        }

        .status-bar {
            background: rgba(116, 75, 162, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            font-size: 12px;
        }

        .list-item {
            border-bottom: 1px solid rgba(0,0,0,0.1);
            padding: 16px 0;
        }

        .list-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <h1 class="page-title">小红书智能体 H5 原型设计</h1>

        <div class="pages-grid">
            <!-- 1. 登录页面 -->
            <div class="phone-frame">
                <div class="page-label">1. 登录页面</div>
                <div class="phone-header">9:41 AM • 100%</div>
                <div class="page-content">
                    <div class="header">
                        <h1>小红书智能体</h1>
                    </div>

                    <div class="card">
                        <h2 style="text-align: center; margin-bottom: 24px; color: #2c3e50;">欢迎登录</h2>

                        <div class="input-group">
                            <label>手机号</label>
                            <input type="tel" placeholder="请输入手机号" value="138****8888">
                        </div>

                        <div class="input-group">
                            <label>密码</label>
                            <input type="password" placeholder="请输入密码" value="••••••••">
                        </div>

                        <div class="input-group">
                            <label>图形验证码</label>
                            <div style="display: flex; gap: 12px;">
                                <input type="text" placeholder="请输入验证码" style="flex: 1;" value="AB3C">
                                <div style="width: 80px; height: 40px; background: #f0f0f0; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px;">AB3C</div>
                            </div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; margin: 16px 0;">
                            <label style="display: flex; align-items: center; font-size: 14px;">
                                <input type="checkbox" style="margin-right: 8px;" checked> 记住登录状态
                            </label>
                            <a href="#" style="color: #764ba2; text-decoration: none; font-size: 14px;">忘记密码？</a>
                        </div>
            <!-- 2. 注册页面 -->
            <div class="phone-frame">
                <div class="page-label">2. 注册页面</div>
                <div class="phone-header">9:41 AM • 100%</div>
                <div class="page-content">
                    <div class="header">
                        <button class="back-btn">← 返回</button>
                        <h1>用户注册</h1>
                        <div></div>
                    </div>

                    <div class="card">
                        <div class="input-group">
                            <label>用户名</label>
                            <input type="text" placeholder="请输入用户名">
                        </div>

                        <div class="input-group">
                            <label>手机号</label>
                            <input type="tel" placeholder="请输入手机号">
                        </div>

                        <div class="input-group">
                            <label>验证码</label>
                            <div style="display: flex; gap: 12px;">
                                <input type="text" placeholder="请输入验证码" style="flex: 1;">
                                <button class="btn-secondary">获取验证码</button>
                            </div>
                        </div>

                        <div class="input-group">
                            <label>密码</label>
                            <input type="password" placeholder="请设置密码">
                        </div>

                        <div class="input-group">
                            <label>确认密码</label>
                            <input type="password" placeholder="请再次输入密码">
                        </div>

                        <div style="margin: 16px 0;">
                            <label style="display: flex; align-items: flex-start; font-size: 14px;">
                                <input type="checkbox" style="margin-right: 8px; margin-top: 2px;">
                                <span>我已阅读并同意<a href="#" style="color: #764ba2;">《用户协议》</a>和<a href="#" style="color: #764ba2;">《隐私政策》</a></span>
                            </label>
                        </div>

                        <button class="btn-primary">注册</button>
                    </div>
                </div>
            </div>

            <!-- 3. 主页 - 文案生成 -->
            <div class="phone-frame">
                <div class="page-label">3. 主页 - 文案生成</div>
                <div class="phone-header">9:41 AM • 100%</div>
                <div class="page-content">
                    <div class="header">
                        <h1>小红书智能体</h1>
                        <div style="display: flex; gap: 8px; align-items: center;">
                            <div class="member-badge">VIP</div>
                            <div class="points-display">1280积分</div>
                        </div>
                    </div>

                    <div class="nav-tabs">
                        <div class="nav-tab active">文案生成</div>
                        <div class="nav-tab">素材库</div>
                        <div class="nav-tab">定制场景</div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #2c3e50;">场景设置</h3>

                        <div class="input-group">
                            <label>行业选择</label>
                            <select>
                                <option>美妆护肤</option>
                                <option>服装时尚</option>
                                <option>美食餐饮</option>
                                <option>旅游出行</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label>受众人群（最多选择2个）</label>
                            <div class="grid">
                                <div class="grid-item" style="background: rgba(102, 126, 234, 0.2); border-color: #667eea;">18-25岁女性</div>
                                <div class="grid-item">26-35岁女性</div>
                                <div class="grid-item" style="background: rgba(102, 126, 234, 0.2); border-color: #667eea;">学生群体</div>
                                <div class="grid-item">职场白领</div>
                            </div>
                        </div>

                        <div class="input-group">
                            <label>发布人身份</label>
                            <div class="grid">
                                <div class="grid-item">品牌官方</div>
                                <div class="grid-item" style="background: rgba(102, 126, 234, 0.2); border-color: #667eea;">KOL达人</div>
                                <div class="grid-item">普通用户</div>
                                <div class="grid-item">专业测评</div>
                            </div>
                        </div>

                        <div class="input-group">
                            <label>产品卖点</label>
                            <textarea rows="3" placeholder="这款面霜含有玻尿酸和烟酰胺成分，能够深层补水保湿，改善肌肤暗沉，适合干性和混合性肌肤使用..."></textarea>
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #2c3e50;">个性化设置</h3>

                        <div class="input-group">
                            <label>选择素材</label>
                            <div style="display: flex; gap: 12px;">
                                <button class="btn-secondary">选择图片</button>
                                <button class="btn-secondary">选择视频</button>
                            </div>
                        </div>

                        <div class="input-group">
                            <label>话题标签</label>
                            <div style="margin-bottom: 12px;">
                                <span style="background: rgba(116, 75, 162, 0.1); padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">#美妆种草</span>
                                <span style="background: rgba(116, 75, 162, 0.1); padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">#护肤心得</span>
                            </div>
                            <input type="text" placeholder="添加自定义话题..." value="#干皮救星">
                        </div>

                        <button class="btn-primary">生成爆款文案 (消耗20积分)</button>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item active">
                        <div style="font-size: 18px; margin-bottom: 4px;">🏠</div>
                        <div>首页</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">📝</div>
                        <div>记录</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">💎</div>
                        <div>充值</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">👤</div>
                        <div>我的</div>
                    </div>
                </div>
            </div>

            <!-- 4. 主页 - 素材库 -->
            <div class="phone-frame">
                <div class="page-label">4. 主页 - 素材库</div>
                <div class="phone-header">9:41 AM • 100%</div>
                <div class="page-content">
                    <div class="header">
                        <h1>小红书智能体</h1>
                        <div style="display: flex; gap: 8px; align-items: center;">
                            <div class="member-badge">VIP</div>
                            <div class="points-display">1280积分</div>
                        </div>
                    </div>

                    <div class="nav-tabs">
                        <div class="nav-tab">文案生成</div>
                        <div class="nav-tab active">素材库</div>
                        <div class="nav-tab">定制场景</div>
                    </div>

                    <div class="nav-tabs" style="margin-bottom: 16px;">
                        <div class="nav-tab active">我的素材</div>
                        <div class="nav-tab">公共素材</div>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h3 style="color: #2c3e50;">我的素材</h3>
                            <button class="btn-secondary">上传素材</button>
                        </div>

                        <div class="input-group">
                            <input type="text" placeholder="搜索素材...">
                        </div>

                        <div class="grid">
                            <div style="aspect-ratio: 1; background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: white;">面霜产品图</div>
                            <div style="aspect-ratio: 1; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: white;">使用效果图</div>
                            <div style="aspect-ratio: 1; background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: white;">成分介绍图</div>
                            <div style="aspect-ratio: 1; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: white;">品牌logo</div>
                        </div>

                        <div style="margin-top: 16px;">
                            <h4 style="margin-bottom: 12px; color: #2c3e50;">素材分类</h4>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                <span style="background: rgba(116, 75, 162, 0.1); padding: 6px 12px; border-radius: 16px; font-size: 12px;">产品图片 (8)</span>
                                <span style="background: rgba(116, 75, 162, 0.1); padding: 6px 12px; border-radius: 16px; font-size: 12px;">效果对比 (3)</span>
                                <span style="background: rgba(116, 75, 162, 0.1); padding: 6px 12px; border-radius: 16px; font-size: 12px;">品牌素材 (2)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item active">
                        <div style="font-size: 18px; margin-bottom: 4px;">🏠</div>
                        <div>首页</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">📝</div>
                        <div>记录</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">💎</div>
                        <div>充值</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">👤</div>
                        <div>我的</div>
                    </div>
                </div>
            </div>

            <!-- 5. 主页 - 定制场景 -->
            <div class="phone-frame">
                <div class="page-label">5. 主页 - 定制场景</div>
                <div class="phone-header">9:41 AM • 100%</div>
                <div class="page-content">
                    <div class="header">
                        <h1>小红书智能体</h1>
                        <div style="display: flex; gap: 8px; align-items: center;">
                            <div class="member-badge">VIP</div>
                            <div class="points-display">1280积分</div>
                        </div>
                    </div>

                    <div class="nav-tabs">
                        <div class="nav-tab">文案生成</div>
                        <div class="nav-tab">素材库</div>
                        <div class="nav-tab active">定制场景</div>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h3 style="color: #2c3e50;">我的场景模板</h3>
                            <button class="btn-secondary">新建模板</button>
                        </div>

                        <div class="grid-item" style="margin-bottom: 12px; border: 2px solid #667eea;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">美妆种草模板</div>
                                    <div style="font-size: 12px; color: #666;">美妆护肤 | 18-25岁女性 | KOL达人</div>
                                    <div style="font-size: 12px; color: #667eea; margin-top: 4px;">使用次数: 15</div>
                                </div>
                                <button class="btn-secondary" style="margin: 0;">使用</button>
                            </div>
                        </div>

                        <div class="grid-item" style="margin-bottom: 12px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">时尚穿搭模板</div>
                                    <div style="font-size: 12px; color: #666;">服装时尚 | 职场白领 | 普通用户</div>
                                    <div style="font-size: 12px; color: #667eea; margin-top: 4px;">使用次数: 8</div>
                                </div>
                                <button class="btn-secondary" style="margin: 0;">使用</button>
                            </div>
                        </div>

                        <div class="grid-item" style="margin-bottom: 12px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">美食探店模板</div>
                                    <div style="font-size: 12px; color: #666;">美食餐饮 | 26-35岁女性 | KOL达人</div>
                                    <div style="font-size: 12px; color: #667eea; margin-top: 4px;">使用次数: 3</div>
                                </div>
                                <button class="btn-secondary" style="margin: 0;">使用</button>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #2c3e50;">快速创建模板</h3>
                        <div class="input-group">
                            <label>模板名称</label>
                            <input type="text" placeholder="请输入模板名称">
                        </div>
                        <button class="btn-primary">基于当前设置创建模板</button>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item active">
                        <div style="font-size: 18px; margin-bottom: 4px;">🏠</div>
                        <div>首页</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">📝</div>
                        <div>记录</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">💎</div>
                        <div>充值</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">👤</div>
                        <div>我的</div>
                    </div>
                </div>
            </div>

            <!-- 6. 积分充值页面 -->
            <div class="phone-frame">
                <div class="page-label">6. 积分充值页面</div>
                <div class="phone-header">9:41 AM • 100%</div>
                <div class="page-content">
                    <div class="header">
                        <button class="back-btn">← 返回</button>
                        <h1>积分充值</h1>
                        <div></div>
                    </div>

                    <div class="card">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="font-size: 24px; font-weight: 600; color: #2c3e50;">当前积分</div>
                            <div style="font-size: 32px; font-weight: 700; color: #667eea; margin-top: 8px;">1,280</div>
                        </div>

                        <h3 style="margin-bottom: 16px; color: #2c3e50;">选择充值套餐</h3>

                        <div class="grid-item" style="margin-bottom: 12px; border: 2px solid transparent;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">基础套餐</div>
                                    <div style="font-size: 12px; color: #666;">500积分</div>
                                </div>
                                <div style="font-weight: 600; color: #667eea;">¥29</div>
                            </div>
                        </div>

                        <div class="grid-item" style="margin-bottom: 12px; border: 2px solid #667eea; background: rgba(102, 126, 234, 0.05);">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">推荐套餐 🔥</div>
                                    <div style="font-size: 12px; color: #666;">1200积分 + 200赠送</div>
                                </div>
                                <div style="font-weight: 600; color: #667eea;">¥69</div>
                            </div>
                        </div>

                        <div class="grid-item" style="margin-bottom: 12px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">超值套餐</div>
                                    <div style="font-size: 12px; color: #666;">2500积分 + 500赠送</div>
                                </div>
                                <div style="font-weight: 600; color: #667eea;">¥139</div>
                            </div>
                        </div>

                        <div style="margin: 20px 0;">
                            <h4 style="margin-bottom: 12px; color: #2c3e50;">支付方式</h4>
                            <div style="display: flex; gap: 12px;">
                                <div style="flex: 1; padding: 12px; border: 2px solid #667eea; border-radius: 8px; text-align: center; background: rgba(102, 126, 234, 0.05);">
                                    <div style="font-size: 20px; margin-bottom: 4px;">💳</div>
                                    <div style="font-size: 12px;">微信支付</div>
                                </div>
                                <div style="flex: 1; padding: 12px; border: 1px solid rgba(0,0,0,0.1); border-radius: 8px; text-align: center;">
                                    <div style="font-size: 20px; margin-bottom: 4px;">💰</div>
                                    <div style="font-size: 12px;">支付宝</div>
                                </div>
                            </div>
                        </div>

                        <button class="btn-primary">立即充值 ¥69</button>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">🏠</div>
                        <div>首页</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">📝</div>
                        <div>记录</div>
                    </div>
                    <div class="nav-item active">
                        <div style="font-size: 18px; margin-bottom: 4px;">💎</div>
                        <div>充值</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">👤</div>
                        <div>我的</div>
                    </div>
                </div>
            </div>
            <!-- 7. 会员中心页面 -->
            <div class="phone-frame">
                <div class="page-label">7. 会员中心页面</div>
                <div class="phone-header">9:41 AM • 100%</div>
                <div class="page-content">
                    <div class="header">
                        <button class="back-btn">← 返回</button>
                        <h1>会员中心</h1>
                        <div></div>
                    </div>

                    <div class="card">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div class="member-badge" style="font-size: 16px; padding: 8px 16px;">VIP会员</div>
                            <div style="font-size: 14px; color: #666; margin-top: 8px;">有效期至：2024-12-31</div>
                            <div style="margin-top: 12px;">
                                <div style="background: rgba(102, 126, 234, 0.1); border-radius: 8px; padding: 12px;">
                                    <div style="font-size: 12px; color: #666; margin-bottom: 4px;">本月已返还积分</div>
                                    <div style="font-size: 20px; font-weight: 600; color: #667eea;">500</div>
                                </div>
                            </div>
                        </div>

                        <h3 style="margin-bottom: 16px; color: #2c3e50;">会员权益</h3>

                        <div style="margin-bottom: 20px;">
                            <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background: rgba(102, 126, 234, 0.05); border-radius: 8px;">
                                <div style="width: 8px; height: 8px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                                <div>
                                    <div style="font-weight: 500;">每月返还500积分</div>
                                    <div style="font-size: 12px; color: #666;">每月1号自动到账</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background: rgba(102, 126, 234, 0.05); border-radius: 8px;">
                                <div style="width: 8px; height: 8px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                                <div>
                                    <div style="font-weight: 500;">使用积分享受8折优惠</div>
                                    <div style="font-size: 12px; color: #666;">生成文案仅需16积分</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background: rgba(102, 126, 234, 0.05); border-radius: 8px;">
                                <div style="width: 8px; height: 8px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                                <div>
                                    <div style="font-weight: 500;">专属素材库访问权限</div>
                                    <div style="font-size: 12px; color: #666;">高质量会员专属素材</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background: rgba(102, 126, 234, 0.05); border-radius: 8px;">
                                <div style="width: 8px; height: 8px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                                <div>
                                    <div style="font-weight: 500;">优先客服支持</div>
                                    <div style="font-size: 12px; color: #666;">专属客服通道</div>
                                </div>
                            </div>
                        </div>

                        <h3 style="margin-bottom: 16px; color: #2c3e50;">续费套餐</h3>

                        <div class="grid-item" style="margin-bottom: 12px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">月度会员</div>
                                    <div style="font-size: 12px; color: #666;">30天有效期</div>
                                </div>
                                <div style="font-weight: 600; color: #667eea;">¥19/月</div>
                            </div>
                        </div>

                        <div class="grid-item" style="margin-bottom: 12px; border: 2px solid #667eea; background: rgba(102, 126, 234, 0.05);">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">年度会员 🔥</div>
                                    <div style="font-size: 12px; color: #666;">365天有效期 (省60元)</div>
                                </div>
                                <div style="font-weight: 600; color: #667eea;">¥168/年</div>
                            </div>
                        </div>

                        <button class="btn-primary">续费会员 ¥168</button>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">🏠</div>
                        <div>首页</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">📝</div>
                        <div>记录</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">💎</div>
                        <div>充值</div>
                    </div>
                    <div class="nav-item active">
                        <div style="font-size: 18px; margin-bottom: 4px;">👤</div>
                        <div>我的</div>
                    </div>
                </div>
            </div>

            <!-- 8. 个人中心页面 -->
            <div class="phone-frame">
                <div class="page-label">8. 个人中心页面</div>
                <div class="phone-header">9:41 AM • 100%</div>
                <div class="page-content">
                    <div class="header">
                        <h1>个人中心</h1>
                        <button class="btn-secondary">退出登录</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; align-items: center; margin-bottom: 20px;">
                            <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; font-weight: 600; margin-right: 16px;">U</div>
                            <div>
                                <div style="font-size: 18px; font-weight: 600; color: #2c3e50;">用户名</div>
                                <div style="font-size: 14px; color: #666;">138****8888</div>
                                <div style="display: flex; gap: 8px; margin-top: 4px;">
                                    <div class="member-badge">VIP会员</div>
                                    <div class="points-display">1280积分</div>
                                </div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; margin-top: 20px;">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 600; color: #667eea;">23</div>
                                <div style="font-size: 12px; color: #666;">生成文案</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 600; color: #667eea;">5</div>
                                <div style="font-size: 12px; color: #666;">场景模板</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 600; color: #667eea;">12</div>
                                <div style="font-size: 12px; color: #666;">上传素材</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #2c3e50;">账户管理</h3>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">⚙️</div>
                                    <span>账户设置</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">🔒</div>
                                    <span>密码修改</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #2c3e50;">我的服务</h3>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">📝</div>
                                    <span>历史记录</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">💎</div>
                                    <span>积分充值</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">👑</div>
                                    <span>会员中心</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #2c3e50;">帮助与支持</h3>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">❓</div>
                                    <span>帮助中心</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">💬</div>
                                    <span>联系客服</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">🏠</div>
                        <div>首页</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">📝</div>
                        <div>记录</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">💎</div>
                        <div>充值</div>
                    </div>
                    <div class="nav-item active">
                        <div style="font-size: 18px; margin-bottom: 4px;">👤</div>
                        <div>我的</div>
                    </div>
                </div>
            </div>

            <!-- 9. 历史记录页面 -->
            <div class="phone-frame">
                <div class="page-label">9. 历史记录页面</div>
                <div class="phone-header">9:41 AM • 100%</div>
                <div class="page-content">
                    <div class="header">
                        <button class="back-btn">← 返回</button>
                        <h1>历史记录</h1>
                        <div></div>
                    </div>

                    <div class="nav-tabs">
                        <div class="nav-tab active">生成记录</div>
                        <div class="nav-tab">使用场景</div>
                        <div class="nav-tab">素材记录</div>
                    </div>

                    <div class="card">
                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                                <div style="font-weight: 500;">美妆种草文案</div>
                                <div style="font-size: 12px; color: #666;">2024-01-15</div>
                            </div>
                            <div style="font-size: 14px; color: #666; margin-bottom: 8px;">行业：美妆护肤 | 受众：18-25岁女性 | 身份：KOL达人</div>
                            <div style="background: rgba(102, 126, 234, 0.1); padding: 12px; border-radius: 8px; font-size: 14px; line-height: 1.4;">
                                "姐妹们！这款面霜真的太好用了！🥰 用了一周皮肤明显变嫩变亮，玻尿酸+烟酰胺的组合真的绝了！干皮姐妹必入！ #美妆种草 #护肤心得 #干皮救星"
                            </div>
                            <div style="margin-top: 8px; display: flex; gap: 8px;">
                                <button class="btn-secondary" style="margin: 0; padding: 6px 12px; font-size: 12px;">编辑</button>
                                <button class="btn-secondary" style="margin: 0; padding: 6px 12px; font-size: 12px;">复制</button>
                                <button class="btn-secondary" style="margin: 0; padding: 6px 12px; font-size: 12px;">分享</button>
                            </div>
                        </div>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                                <div style="font-weight: 500;">时尚穿搭文案</div>
                                <div style="font-size: 12px; color: #666;">2024-01-14</div>
                            </div>
                            <div style="font-size: 14px; color: #666; margin-bottom: 8px;">行业：服装时尚 | 受众：职场白领 | 身份：普通用户</div>
                            <div style="background: rgba(102, 126, 234, 0.1); padding: 12px; border-radius: 8px; font-size: 14px; line-height: 1.4;">
                                "职场穿搭新思路！这套搭配既优雅又不失活力💼✨ 简约的西装外套配上有设计感的内搭，瞬间提升气质！上班族必备！ #职场穿搭 #时尚分享"
                            </div>
                            <div style="margin-top: 8px; display: flex; gap: 8px;">
                                <button class="btn-secondary" style="margin: 0; padding: 6px 12px; font-size: 12px;">编辑</button>
                                <button class="btn-secondary" style="margin: 0; padding: 6px 12px; font-size: 12px;">复制</button>
                                <button class="btn-secondary" style="margin: 0; padding: 6px 12px; font-size: 12px;">分享</button>
                            </div>
                        </div>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                                <div style="font-weight: 500;">美食探店文案</div>
                                <div style="font-size: 12px; color: #666;">2024-01-13</div>
                            </div>
                            <div style="font-size: 14px; color: #666; margin-bottom: 8px;">行业：美食餐饮 | 受众：26-35岁女性 | 身份：KOL达人</div>
                            <div style="background: rgba(102, 126, 234, 0.1); padding: 12px; border-radius: 8px; font-size: 14px; line-height: 1.4;">
                                "今天发现了一家宝藏日料店！🍣 环境超棒，食材新鲜，师傅手艺一流！特别推荐他家的三文鱼刺身和手握寿司，入口即化！ #美食探店 #日料推荐"
                            </div>
                            <div style="margin-top: 8px; display: flex; gap: 8px;">
                                <button class="btn-secondary" style="margin: 0; padding: 6px 12px; font-size: 12px;">编辑</button>
                                <button class="btn-secondary" style="margin: 0; padding: 6px 12px; font-size: 12px;">复制</button>
                                <button class="btn-secondary" style="margin: 0; padding: 6px 12px; font-size: 12px;">分享</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">🏠</div>
                        <div>首页</div>
                    </div>
                    <div class="nav-item active">
                        <div style="font-size: 18px; margin-bottom: 4px;">📝</div>
                        <div>记录</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">💎</div>
                        <div>充值</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">👤</div>
                        <div>我的</div>
                    </div>
                </div>
            </div>

            <!-- 10. 帮助中心页面 -->
            <div class="phone-frame">
                <div class="page-label">10. 帮助中心页面</div>
                <div class="phone-header">9:41 AM • 100%</div>
                <div class="page-content">
                    <div class="header">
                        <button class="back-btn">← 返回</button>
                        <h1>帮助中心</h1>
                        <div></div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #2c3e50;">常见问题</h3>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 500; margin-bottom: 8px; color: #2c3e50;">❓ 如何生成高质量文案？</div>
                                <div style="font-size: 14px; color: #666; line-height: 1.4;">详细填写产品卖点，选择准确的行业和受众，添加相关话题标签，这样能帮助AI生成更精准的文案。建议描述产品的核心特点、使用场景和目标用户痛点。</div>
                            </div>
                        </div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 500; margin-bottom: 8px; color: #2c3e50;">💎 积分如何使用？</div>
                                <div style="font-size: 14px; color: #666; line-height: 1.4;">每次生成文案消耗20积分，会员用户享受8折优惠仅需16积分。可通过充值或购买会员获得积分。积分永久有效，不会过期。</div>
                            </div>
                        </div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 500; margin-bottom: 8px; color: #2c3e50;">📸 如何上传和管理素材？</div>
                                <div style="font-size: 14px; color: #666; line-height: 1.4;">在素材库页面点击"上传素材"，支持JPG、PNG格式图片，单张不超过5MB。可以创建分类文件夹管理素材，支持搜索和标签功能。</div>
                            </div>
                        </div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 500; margin-bottom: 8px; color: #2c3e50;">👑 会员有什么特权？</div>
                                <div style="font-size: 14px; color: #666; line-height: 1.4;">VIP会员享受每月500积分返还、生成文案8折优惠、专属素材库访问权限、优先客服支持等特权。年费会员更优惠。</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #2c3e50;">使用教程</h3>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">📖</div>
                                    <span>新手入门指南</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">✍️</div>
                                    <span>文案生成技巧</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">🎨</div>
                                    <span>素材管理教程</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>

                        <div class="list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 18px; margin-right: 12px;">🔧</div>
                                    <span>场景模板使用</span>
                                </div>
                                <span style="color: #666;">></span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #2c3e50;">联系我们</h3>

                        <div style="text-align: center;">
                            <div style="font-size: 14px; color: #666; margin-bottom: 12px;">技术支持微信</div>
                            <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 14px;">
                                <div style="text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 4px;">📱</div>
                                    <div>扫码添加</div>
                                </div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 16px;">扫码添加客服微信</div>

                            <div style="background: rgba(102, 126, 234, 0.1); padding: 12px; border-radius: 8px;">
                                <div style="font-size: 14px; color: #2c3e50; margin-bottom: 4px;">客服时间</div>
                                <div style="font-size: 12px; color: #666;">工作日 9:00-18:00</div>
                                <div style="font-size: 12px; color: #666;">周末 10:00-17:00</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">🏠</div>
                        <div>首页</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">📝</div>
                        <div>记录</div>
                    </div>
                    <div class="nav-item">
                        <div style="font-size: 18px; margin-bottom: 4px;">💎</div>
                        <div>充值</div>
                    </div>
                    <div class="nav-item active">
                        <div style="font-size: 18px; margin-bottom: 4px;">👤</div>
                        <div>我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>