{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-18T09:36:30.991Z", "updatedAt": "2025-06-18T09:36:30.995Z", "resourceCount": 15}, "resources": [{"id": "b2b-product-operations", "source": "project", "protocol": "role", "name": "B2b Product Operations 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/b2b-product-operations/b2b-product-operations.role.md", "metadata": {"createdAt": "2025-06-18T09:36:30.991Z", "updatedAt": "2025-06-18T09:36:30.991Z", "scannedAt": "2025-06-18T09:36:30.991Z"}}, {"id": "b2b-operations", "source": "project", "protocol": "thought", "name": "B2b Operations 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/b2b-product-operations/thought/b2b-operations.thought.md", "metadata": {"createdAt": "2025-06-18T09:36:30.991Z", "updatedAt": "2025-06-18T09:36:30.991Z", "scannedAt": "2025-06-18T09:36:30.991Z"}}, {"id": "b2b-operations", "source": "project", "protocol": "execution", "name": "B2b Operations 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/b2b-product-operations/execution/b2b-operations.execution.md", "metadata": {"createdAt": "2025-06-18T09:36:30.992Z", "updatedAt": "2025-06-18T09:36:30.992Z", "scannedAt": "2025-06-18T09:36:30.992Z"}}, {"id": "coal-mine-expert", "source": "project", "protocol": "role", "name": "Coal Mine Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/coal-mine-expert/coal-mine-expert.role.md", "metadata": {"createdAt": "2025-06-18T09:36:30.992Z", "updatedAt": "2025-06-18T09:36:30.992Z", "scannedAt": "2025-06-18T09:36:30.992Z"}}, {"id": "coal-mine-expertise", "source": "project", "protocol": "thought", "name": "Coal Mine Expertise 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/coal-mine-expert/thought/coal-mine-expertise.thought.md", "metadata": {"createdAt": "2025-06-18T09:36:30.992Z", "updatedAt": "2025-06-18T09:36:30.992Z", "scannedAt": "2025-06-18T09:36:30.992Z"}}, {"id": "coal-mine-operations", "source": "project", "protocol": "execution", "name": "Coal Mine Operations 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/coal-mine-expert/execution/coal-mine-operations.execution.md", "metadata": {"createdAt": "2025-06-18T09:36:30.992Z", "updatedAt": "2025-06-18T09:36:30.992Z", "scannedAt": "2025-06-18T09:36:30.992Z"}}, {"id": "frontend-ui-engineer", "source": "project", "protocol": "role", "name": "Frontend Ui Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/frontend-ui-engineer/frontend-ui-engineer.role.md", "metadata": {"createdAt": "2025-06-18T09:36:30.993Z", "updatedAt": "2025-06-18T09:36:30.993Z", "scannedAt": "2025-06-18T09:36:30.993Z"}}, {"id": "frontend-design-thinking", "source": "project", "protocol": "thought", "name": "Frontend Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/frontend-ui-engineer/thought/frontend-design-thinking.thought.md", "metadata": {"createdAt": "2025-06-18T09:36:30.993Z", "updatedAt": "2025-06-18T09:36:30.993Z", "scannedAt": "2025-06-18T09:36:30.993Z"}}, {"id": "frontend-design-workflow", "source": "project", "protocol": "execution", "name": "Frontend Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/frontend-ui-engineer/execution/frontend-design-workflow.execution.md", "metadata": {"createdAt": "2025-06-18T09:36:30.993Z", "updatedAt": "2025-06-18T09:36:30.993Z", "scannedAt": "2025-06-18T09:36:30.993Z"}}, {"id": "prompt-engineer", "source": "project", "protocol": "role", "name": "Prompt Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/prompt-engineer/prompt-engineer.role.md", "metadata": {"createdAt": "2025-06-18T09:36:30.993Z", "updatedAt": "2025-06-18T09:36:30.993Z", "scannedAt": "2025-06-18T09:36:30.993Z"}}, {"id": "prompt-design", "source": "project", "protocol": "thought", "name": "Prompt Design 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/prompt-engineer/thought/prompt-design.thought.md", "metadata": {"createdAt": "2025-06-18T09:36:30.994Z", "updatedAt": "2025-06-18T09:36:30.994Z", "scannedAt": "2025-06-18T09:36:30.994Z"}}, {"id": "prompt-creation", "source": "project", "protocol": "execution", "name": "Prompt Creation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/prompt-engineer/execution/prompt-creation.execution.md", "metadata": {"createdAt": "2025-06-18T09:36:30.994Z", "updatedAt": "2025-06-18T09:36:30.994Z", "scannedAt": "2025-06-18T09:36:30.994Z"}}, {"id": "ui-ux-designer", "source": "project", "protocol": "role", "name": "Ui Ux Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/ui-ux-designer/ui-ux-designer.role.md", "metadata": {"createdAt": "2025-06-18T09:36:30.994Z", "updatedAt": "2025-06-18T09:36:30.994Z", "scannedAt": "2025-06-18T09:36:30.994Z"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-06-18T09:36:30.994Z", "updatedAt": "2025-06-18T09:36:30.994Z", "scannedAt": "2025-06-18T09:36:30.994Z"}}, {"id": "design-process", "source": "project", "protocol": "execution", "name": "Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/execution/design-process.execution.md", "metadata": {"createdAt": "2025-06-18T09:36:30.994Z", "updatedAt": "2025-06-18T09:36:30.994Z", "scannedAt": "2025-06-18T09:36:30.994Z"}}], "stats": {"totalResources": 15, "byProtocol": {"role": 5, "thought": 5, "execution": 5}, "bySource": {"project": 15}}}