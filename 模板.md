# 博客网站开发PRD文档
## 1. 项目概述
### 1.1 项目背景
开发一个简约现代风格的英文博客网站，支持英中双语切换。该网站主要用于展示公众号文章创作者的内容，采用基于文件系统的内容管理方式，无需数据库支持。

### 1.2 项目目标
创建一个简约现代风格的博客网站
支持英中双语切换
实现基于文件系统的内容管理
提供良好的阅读体验和响应式设计
## 2. 技术栈
### 2.1 前端框架
Next.js (React框架)
### 2.2 UI组件和样式
Shadcn UI 和 Radix UI 作为组件基础
Tailwind CSS 实现响应式设计，采用移动优先方法
Stylus 作为 CSS 模块，实现组件特定样式
利用 Stylus 功能（嵌套、变量和 mixins）实现高效样式
在 Stylus 模块内为 CSS 类实现一致的命名约定
混合样式方法：
使用 Tailwind 实现常用实用程序和布局
使用 Stylus 模块实现复杂的组件特定样式
不使用 @apply 指令
### 2.3 国际化
next-i18next 或 next-intl 实现网站界面的英中双语切换
网站界面元素（导航栏、按钮、提示文本等）支持英中双语切换
文章内容保持原样，不进行自动翻译
## 3. 功能需求
### 3.1 内容管理
基于文件系统的内容管理方式
支持直接将HTML文章文件放入指定文件夹，系统自动识别并显示
从HTML文件的meta标签和内容中提取元数据（标题、描述、作者、日期、封面图等）
例如从<meta content="Aitrainee" name="author">提取作者信息
从<meta content="从0到1用AI做了个AI服务网站, 全程没写一行代码。" property="og:title">提取标题
从<meta content="https://mmbiz.qpic.cn/..." property="og:image">提取封面图
自动检测文章语言（英文/中文）
支持保留原HTML文章的样式和格式
支持同一篇文章的多语言版本管理：
提供专门的文件夹结构：articles/en/ 和 articles/zh/ 分别存放英文和中文文章
文件命名约定：
同一篇文章的不同语言版本必须使用相同的文件名（如 article1.html）
系统将通过相同的文件名在不同语言文件夹中查找对应的翻译版本
例如：articles/en/my-blog-post.html 和 articles/zh/my-blog-post.html 将被识别为同一篇文章的不同语言版本
支持作者在任意时间添加任一语言版本，系统自动识别并关联
当网站语言切换时，如果存在当前文章的对应语言版本，则自动切换到该版本
如果不存在对应语言版本，则保持显示当前版本并提供提示
#### 3.1.1 示例HTML文件结构
开发者应支持以下HTML文件格式和元数据提取：
```html
<!DOCTYPE html>
<html>
<head>
  <meta content="文章标题" property="og:title">
  <meta content="文章描述" name="description">
  <meta content="作者名称" name="author">
  <meta content="https://example.com/image.jpg" property="og:image">
  <!-- 其他元数据 -->
  <style>
    /* 文章内部样式，需要保留 */
  </style>
</head>
<body>
  <!-- 文章内容，需要完整保留格式和样式 -->
</body>
</html>
```

### 3.2 页面结构
首页：展示最新文章列表
文章详情页：展示完整文章内容
关于页：介绍作者信息
联系页：提供联系方式
### 3.3 用户界面
响应式设计，适配各种设备尺寸
支持深色/浅色模式切换
语言切换功能（英文/中文）
简约现代风格的UI设计
### 3.4 功能优先级
P0（必须实现）：

基本文章展示功能
网站界面的英中双语切换
保留原HTML文章样式和格式
响应式设计
P1（重要功能）：

文章多语言版本自动关联与切换
深色/浅色模式切换
文章元数据提取
图片跨域问题处理
P2（次要功能）：

性能优化与缓存机制
SEO优化
高级安全性配置
## 4. 技术实现
### 4.1 项目结构
```txt
blog2/
├── public/
│   ├── locales/          # 国际化文件
│   │   ├── en/
│   │   └── zh/
│   └── articles/         # 存放HTML文章的文件夹
│       ├── en/           # 英文文章
│       └── zh/           # 中文文章
├── src/
│   ├── components/       # UI组件
│   │   ├── common/       # 通用组件
│   │   ├── layout/       # 布局组件
│   │   ├── blog/         # 博客相关组件
│   │   └── ui/           # UI组件
│   ├── pages/            # 页面
│   ├── styles/           # 样式文件
│   │   ├── main.styl     # 主Stylus文件
│   │   └── components/   # 组件特定样式
│   ├── lib/              # 工具函数
│   ├── hooks/            # 自定义hooks
│   └── types/            # TypeScript类型定义
├── next.config.js        # Next.js配置
├── tailwind.config.js    # Tailwind配置
└── package.json
```

### 4.2 文章解析器
使用JSDOM解析HTML文件
从HTML的meta标签中提取元数据（标题、描述、作者等）
处理文章内容，保留原格式和样式
自动检测文章语言
生成文章列表和文章详情
保留原有图片链接，并通过以下方式处理可能的跨域问题：
添加适当的CORS头部配置在next.config.js中
使用图片代理服务处理外部图片资源
为外部图片添加referrerpolicy="no-referrer"属性
提取并整合原HTML中的样式，包括：
内联样式
样式标签中的CSS规则
外部样式表引用
### 4.3 国际化实现
使用next-i18next实现网站界面的多语言支持
提供语言切换功能，影响整个网站的UI元素
为不同语言提供翻译文件
实现文章多语言版本的自动切换机制：
通过相同的文件名在不同语言文件夹中查找对应的翻译版本
建立同一文章不同语言版本之间的映射关系
在用户切换语言时，检查是否存在当前文章的目标语言版本
如存在，则切换到对应语言版本；如不存在，则保持当前版本
### 4.4 主题切换
支持深色/浅色模式切换
使用next-themes管理主题
### 4.5 性能优化
实现文章缓存机制
首次解析文章后将结果缓存在内存中
使用LRU（最近最少使用）缓存策略管理内存使用
定期检查文件更新时间，只在文件有更新时重新解析
实现增量静态再生成(ISR)，只在文章有更新时重新生成页面
优化图片加载，使用Next.js的Image组件处理外部图片
### 4.6 错误处理与安全性
实现HTML解析错误处理机制
捕获并记录解析错误
提供友好的错误页面
在开发环境中显示详细错误信息
实现内容安全策略
配置适当的Content-Security-Policy头
使用DOMPurify等库净化HTML内容，防止XSS攻击
为外部资源添加适当的安全属性
### 4.7 SEO优化
自动生成sitemap.xml
配置robots.txt
为每篇文章生成适当的meta标签
实现结构化数据（Schema.org）标记
支持Open Graph和Twitter Cards标签
## 5. 用户体验
### 5.1 响应式设计
移动优先的设计方法
适配不同设备尺寸（手机、平板、桌面）
### 5.2 性能优化
使用Next.js的静态生成功能提高页面加载速度
定期重新生成页面以更新内容（每小时一次）
### 5.3 可访问性
确保网站符合WCAG可访问性标准
提供适当的颜色对比度和键盘导航支持
## 6. 部署和维护
### 6.1 部署方式
优先使用Vercel进行部署，利用其对Next.js的原生支持
部署配置：
设置构建命令：npm run build
输出目录：.next
环境变量：根据需要配置（如API密钥等）
利用Vercel的自动预览功能进行部署前测试
配置自定义域名和HTTPS
也可选择Netlify等其他静态网站托管平台
支持自动化部署流程
### 6.2 内容更新
添加新文章：将HTML文件放入对应语言的文件夹中，系统自动识别并显示
更新现有文章：
直接覆盖原文件，系统将在下次构建或重新部署时更新内容
对于增量静态再生成(ISR)模式，系统将在设定的重新验证时间后自动更新
删除文章：
从文件夹中删除HTML文件
系统将在下次构建时自动从文章列表中移除该文章
提供配置选项，决定是否保留已删除文章的URL（返回404或重定向）
文章版本控制：
可选择使用Git进行文章版本管理
系统只显示最新版本的文章
## 7. 未来扩展
### 7.1 潜在功能
文章搜索功能
文章分类和标签系统
评论系统集成
社交媒体分享功能
订阅功能
## 8. 时间线和里程碑
### 8.1 开发阶段
阶段1：基础架构搭建和UI组件开发
阶段2：文章解析器和内容管理系统实现
阶段3：国际化和主题切换功能实现
阶段4：测试、优化和部署
### 8.2 交付物
完整的博客网站代码
部署指南和使用文档
内容管理指南