# 小红书智能体H5用户端需求清单

## 1. 登录注册系统

### 1.1 注册功能
- 用户设置用户名、手机号、密码进行注册
- 手机验证码验证（需要图形验证码防攻击）
- 隐私政策和用户协议确认

### 1.2 登录功能
- 手机号+密码登录
- 图形验证码验证
- 记住登录状态选项

### 1.3 账户安全
- 密码修改功能
  - 原密码验证方式
  - 手机验证码找回方式
- 账户信息保护措施

## 2. 主页功能

### 2.1 素材库功能
- 私有素材管理
  - 素材上传功能（图片）
  - 素材分类管理
  - 素材搜索功能
- 公共素材浏览
  - 公用图库浏览
  - 会员专属图库（会员可见）

### 2.2 爆款文案生成

#### 2.2.1 场景设置
- 行业选择（下拉菜单）
- 受众人群选择（多选，最多两个）
- 发布人身份选择（单选）
- 产品卖点编辑区

#### 2.2.2 个性化设置
- 素材选择功能（图片/视频）
- 话题添加功能
  - 固定话题选择
  - 自定义话题添加

#### 2.2.3 文案生成与编辑
- 生成按钮（消耗积分）
- 生成结果展示
- 二次编辑功能
- 结果保存功能

#### 2.2.4 分享功能
- 小红书二维码生成
- 一键发布到小红书功能

### 2.3 定制场景功能
- 场景模板保存
  - 保存用户自定义场景
  - 场景模板管理
- 场景模板使用
  - 快速调用已保存场景
  - 仅更新素材和话题

## 3. 积分与会员系统

### 3.1 积分管理
- 积分充值功能
  - 不同额度充值选项
  - 支付方式选择
- 积分使用明细
  - 积分消费记录
  - 剩余积分显示

### 3.2 会员功能
- 会员购买选项
  - 不同时长会员套餐
  - 会员权益说明
- 会员专属权益
  - 每月积分返还
  - 使用积分折扣优惠
  - 专属素材和模板

## 4. 个人中心

### 4.1 账户管理
- 个人信息展示
- 账户设置功能
- 密码修改入口

### 4.2 历史记录
- 已生成文案历史
- 使用过的场景记录
- 素材使用记录

### 4.3 会员与积分
- 会员状态展示
- 积分余额显示
- 充值入口

## 5. 帮助与支持

### 5.1 帮助中心
- 常见问题解答
- 使用教程

### 5.2 联系支持
- 技术支持微信联系方式
- 问题反馈通道

## 6. 界面设计要求

### 6.1 整体风格
- 符合小红书平台审美
- 简洁、直观的操作界面
- 关键功能突出展示

### 6.2 交互体验
- 流畅的页面过渡效果
- 操作反馈及提示
- 适配各类移动设备屏幕
