<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书智能体 - 原型设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            min-height: 100vh;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .page {
            display: none;
            padding: 20px;
            min-height: 100vh;
        }

        .page.active {
            display: block;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
        }

        .back-btn {
            background: rgba(116, 75, 162, 0.1);
            border: none;
            padding: 8px 12px;
            border-radius: 8px;
            color: #764ba2;
            cursor: pointer;
            font-size: 14px;
        }

        .card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .input-group {
            margin-bottom: 16px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .input-group input,
        .input-group select,
        .input-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            margin-top: 16px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: rgba(116, 75, 162, 0.1);
            color: #764ba2;
            border: 1px solid rgba(116, 75, 162, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            margin: 5px;
        }

        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .nav-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .grid-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 16px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .grid-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 414px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            display: flex;
            padding: 12px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            cursor: pointer;
            color: #666;
            font-size: 12px;
        }

        .nav-item.active {
            color: #764ba2;
        }

        .status-bar {
            background: rgba(116, 75, 162, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            font-size: 12px;
            margin-bottom: 16px;
        }

        .progress-bar {
            background: rgba(0, 0, 0, 0.1);
            height: 4px;
            border-radius: 2px;
            overflow: hidden;
            margin: 16px 0;
        }

        .progress-fill {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100%;
            width: 60%;
            border-radius: 2px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .member-badge {
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            color: #333;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        .points-display {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录页面 -->
        <div class="page active" id="login">
            <div class="header">
                <h1>小红书智能体</h1>
            </div>

            <div class="card">
                <h2 style="text-align: center; margin-bottom: 24px; color: #2c3e50;">欢迎登录</h2>

                <div class="input-group">
                    <label>手机号</label>
                    <input type="tel" placeholder="请输入手机号">
                </div>

                <div class="input-group">
                    <label>密码</label>
                    <input type="password" placeholder="请输入密码">
                </div>

                <div class="input-group">
                    <label>图形验证码</label>
                    <div style="display: flex; gap: 12px;">
                        <input type="text" placeholder="请输入验证码" style="flex: 1;">
                        <div style="width: 80px; height: 40px; background: #f0f0f0; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px;">验证码</div>
                    </div>
                </div>

                <div style="display: flex; justify-content: space-between; align-items: center; margin: 16px 0;">
                    <label style="display: flex; align-items: center; font-size: 14px;">
                        <input type="checkbox" style="margin-right: 8px;"> 记住登录状态
                    </label>
                    <a href="#" style="color: #764ba2; text-decoration: none; font-size: 14px;">忘记密码？</a>
                </div>

                <button class="btn-primary" onclick="showPage('home')">登录</button>

                <div style="text-align: center; margin-top: 20px;">
                    <span style="color: #666; font-size: 14px;">还没有账号？</span>
                    <a href="#" onclick="showPage('register')" style="color: #764ba2; text-decoration: none;">立即注册</a>
                </div>
            </div>
        </div>

        <!-- 注册页面 -->
        <div class="page" id="register">
            <div class="header">
                <button class="back-btn" onclick="showPage('login')">← 返回</button>
                <h1>用户注册</h1>
                <div></div>
            </div>

            <div class="card">
                <div class="input-group">
                    <label>用户名</label>
                    <input type="text" placeholder="请输入用户名">
                </div>

                <div class="input-group">
                    <label>手机号</label>
                    <input type="tel" placeholder="请输入手机号">
                </div>

                <div class="input-group">
                    <label>验证码</label>
                    <div style="display: flex; gap: 12px;">
                        <input type="text" placeholder="请输入验证码" style="flex: 1;">
                        <button class="btn-secondary">获取验证码</button>
                    </div>
                </div>

                <div class="input-group">
                    <label>密码</label>
                    <input type="password" placeholder="请设置密码">
                </div>

                <div class="input-group">
                    <label>确认密码</label>
                    <input type="password" placeholder="请再次输入密码">
                </div>

                <div style="margin: 16px 0;">
                    <label style="display: flex; align-items: flex-start; font-size: 14px;">
                        <input type="checkbox" style="margin-right: 8px; margin-top: 2px;">
                        <span>我已阅读并同意<a href="#" style="color: #764ba2;">《用户协议》</a>和<a href="#" style="color: #764ba2;">《隐私政策》</a></span>
                    </label>
                </div>

                <button class="btn-primary" onclick="showPage('login')">注册</button>
            </div>
        </div>

        <!-- 主页 -->
        <div class="page" id="home">
            <div class="header">
                <h1>小红书智能体</h1>
                <div style="display: flex; gap: 8px; align-items: center;">
                    <div class="member-badge">VIP</div>
                    <div class="points-display">1280积分</div>
                </div>
            </div>

            <div class="nav-tabs">
                <div class="nav-tab active" onclick="showTab('content-gen')">文案生成</div>
                <div class="nav-tab" onclick="showTab('material-lib')">素材库</div>
                <div class="nav-tab" onclick="showTab('custom-scene')">定制场景</div>
            </div>

            <!-- 文案生成标签页 -->
            <div class="tab-content active" id="content-gen">
                <div class="card">
                    <h3 style="margin-bottom: 16px; color: #2c3e50;">场景设置</h3>

                    <div class="input-group">
                        <label>行业选择</label>
                        <select>
                            <option>请选择行业</option>
                            <option>美妆护肤</option>
                            <option>服装时尚</option>
                            <option>美食餐饮</option>
                            <option>旅游出行</option>
                            <option>数码科技</option>
                            <option>家居生活</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <label>受众人群（最多选择2个）</label>
                        <div class="grid">
                            <div class="grid-item">18-25岁女性</div>
                            <div class="grid-item">26-35岁女性</div>
                            <div class="grid-item">学生群体</div>
                            <div class="grid-item">职场白领</div>
                            <div class="grid-item">宝妈群体</div>
                            <div class="grid-item">时尚达人</div>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>发布人身份</label>
                        <div class="grid">
                            <div class="grid-item">品牌官方</div>
                            <div class="grid-item">KOL达人</div>
                            <div class="grid-item">普通用户</div>
                            <div class="grid-item">专业测评</div>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>产品卖点</label>
                        <textarea rows="3" placeholder="请描述产品的主要卖点和特色..."></textarea>
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; color: #2c3e50;">个性化设置</h3>

                    <div class="input-group">
                        <label>选择素材</label>
                        <div style="display: flex; gap: 12px;">
                            <button class="btn-secondary">选择图片</button>
                            <button class="btn-secondary">选择视频</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>话题标签</label>
                        <div style="margin-bottom: 12px;">
                            <span style="background: rgba(116, 75, 162, 0.1); padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">#美妆种草</span>
                            <span style="background: rgba(116, 75, 162, 0.1); padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">#护肤心得</span>
                        </div>
                        <input type="text" placeholder="添加自定义话题...">
                    </div>

                    <button class="btn-primary">生成爆款文案 (消耗20积分)</button>
                </div>
            </div>

            <!-- 素材库标签页 -->
            <div class="tab-content" id="material-lib">
                <div class="nav-tabs" style="margin-bottom: 16px;">
                    <div class="nav-tab active">我的素材</div>
                    <div class="nav-tab">公共素材</div>
                </div>

                <div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h3 style="color: #2c3e50;">我的素材</h3>
                        <button class="btn-secondary">上传素材</button>
                    </div>

                    <div class="input-group">
                        <input type="text" placeholder="搜索素材...">
                    </div>

                    <div class="grid">
                        <div style="aspect-ratio: 1; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">图片1</div>
                        <div style="aspect-ratio: 1; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">图片2</div>
                        <div style="aspect-ratio: 1; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">图片3</div>
                        <div style="aspect-ratio: 1; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">图片4</div>
                    </div>
                </div>
            </div>

            <!-- 定制场景标签页 -->
            <div class="tab-content" id="custom-scene">
                <div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h3 style="color: #2c3e50;">我的场景模板</h3>
                        <button class="btn-secondary">新建模板</button>
                    </div>

                    <div class="grid-item" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 500;">美妆种草模板</div>
                                <div style="font-size: 12px; color: #666;">美妆护肤 | 18-25岁女性</div>
                            </div>
                            <button class="btn-secondary" style="margin: 0;">使用</button>
                        </div>
                    </div>

                    <div class="grid-item" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 500;">时尚穿搭模板</div>
                                <div style="font-size: 12px; color: #666;">服装时尚 | 职场白领</div>
                            </div>
                            <button class="btn-secondary" style="margin: 0;">使用</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 积分充值页面 -->
        <div class="page" id="recharge">
            <div class="header">
                <button class="back-btn" onclick="showPage('profile')">← 返回</button>
                <h1>积分充值</h1>
                <div></div>
            </div>

            <div class="card">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 24px; font-weight: 600; color: #2c3e50;">当前积分</div>
                    <div style="font-size: 32px; font-weight: 700; color: #667eea; margin-top: 8px;">1,280</div>
                </div>

                <h3 style="margin-bottom: 16px; color: #2c3e50;">选择充值套餐</h3>

                <div class="grid-item" style="margin-bottom: 12px; border: 2px solid transparent;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 500;">基础套餐</div>
                            <div style="font-size: 12px; color: #666;">500积分</div>
                        </div>
                        <div style="font-weight: 600; color: #667eea;">¥29</div>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px; border: 2px solid #667eea;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 500;">推荐套餐</div>
                            <div style="font-size: 12px; color: #666;">1200积分 + 200赠送</div>
                        </div>
                        <div style="font-weight: 600; color: #667eea;">¥69</div>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 500;">超值套餐</div>
                            <div style="font-size: 12px; color: #666;">2500积分 + 500赠送</div>
                        </div>
                        <div style="font-weight: 600; color: #667eea;">¥139</div>
                    </div>
                </div>

                <button class="btn-primary">立即充值</button>
            </div>
        </div>

        <!-- 会员购买页面 -->
        <div class="page" id="membership">
            <div class="header">
                <button class="back-btn" onclick="showPage('profile')">← 返回</button>
                <h1>会员中心</h1>
                <div></div>
            </div>

            <div class="card">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div class="member-badge" style="font-size: 16px; padding: 8px 16px;">VIP会员</div>
                    <div style="font-size: 14px; color: #666; margin-top: 8px;">有效期至：2024-12-31</div>
                </div>

                <h3 style="margin-bottom: 16px; color: #2c3e50;">会员权益</h3>

                <div style="margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div style="width: 6px; height: 6px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                        <span>每月返还500积分</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div style="width: 6px; height: 6px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                        <span>使用积分享受8折优惠</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div style="width: 6px; height: 6px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                        <span>专属素材库访问权限</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div style="width: 6px; height: 6px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                        <span>优先客服支持</span>
                    </div>
                </div>

                <h3 style="margin-bottom: 16px; color: #2c3e50;">续费套餐</h3>

                <div class="grid-item" style="margin-bottom: 12px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 500;">月度会员</div>
                            <div style="font-size: 12px; color: #666;">30天有效期</div>
                        </div>
                        <div style="font-weight: 600; color: #667eea;">¥19/月</div>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px; border: 2px solid #667eea;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 500;">年度会员</div>
                            <div style="font-size: 12px; color: #666;">365天有效期 (省60元)</div>
                        </div>
                        <div style="font-weight: 600; color: #667eea;">¥168/年</div>
                    </div>
                </div>

                <button class="btn-primary">续费会员</button>
            </div>
        </div>
        <!-- 个人中心页面 -->
        <div class="page" id="profile">
            <div class="header">
                <h1>个人中心</h1>
                <button class="btn-secondary" onclick="showPage('login')">退出登录</button>
            </div>

            <div class="card">
                <div style="display: flex; align-items: center; margin-bottom: 20px;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; font-weight: 600; margin-right: 16px;">U</div>
                    <div>
                        <div style="font-size: 18px; font-weight: 600; color: #2c3e50;">用户名</div>
                        <div style="font-size: 14px; color: #666;">138****8888</div>
                        <div style="display: flex; gap: 8px; margin-top: 4px;">
                            <div class="member-badge">VIP会员</div>
                            <div class="points-display">1280积分</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3 style="margin-bottom: 16px; color: #2c3e50;">账户管理</h3>

                <div class="grid-item" style="margin-bottom: 12px;" onclick="showPage('account-settings')">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>账户设置</span>
                        <span style="color: #666;">></span>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>密码修改</span>
                        <span style="color: #666;">></span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3 style="margin-bottom: 16px; color: #2c3e50;">我的服务</h3>

                <div class="grid-item" style="margin-bottom: 12px;" onclick="showPage('history')">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>历史记录</span>
                        <span style="color: #666;">></span>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px;" onclick="showPage('recharge')">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>积分充值</span>
                        <span style="color: #666;">></span>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px;" onclick="showPage('membership')">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>会员中心</span>
                        <span style="color: #666;">></span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3 style="margin-bottom: 16px; color: #2c3e50;">帮助与支持</h3>

                <div class="grid-item" style="margin-bottom: 12px;" onclick="showPage('help')">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>帮助中心</span>
                        <span style="color: #666;">></span>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>联系客服</span>
                        <span style="color: #666;">></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史记录页面 -->
        <div class="page" id="history">
            <div class="header">
                <button class="back-btn" onclick="showPage('profile')">← 返回</button>
                <h1>历史记录</h1>
                <div></div>
            </div>

            <div class="nav-tabs">
                <div class="nav-tab active">生成记录</div>
                <div class="nav-tab">使用场景</div>
                <div class="nav-tab">素材记录</div>
            </div>

            <div class="card">
                <div style="border-bottom: 1px solid rgba(0,0,0,0.1); padding-bottom: 16px; margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                        <div style="font-weight: 500;">美妆种草文案</div>
                        <div style="font-size: 12px; color: #666;">2024-01-15</div>
                    </div>
                    <div style="font-size: 14px; color: #666; margin-bottom: 8px;">行业：美妆护肤 | 受众：18-25岁女性</div>
                    <div style="background: rgba(102, 126, 234, 0.1); padding: 8px; border-radius: 6px; font-size: 14px;">姐妹们！这款面霜真的太好用了！用了一周皮肤明显变嫩...</div>
                </div>

                <div style="border-bottom: 1px solid rgba(0,0,0,0.1); padding-bottom: 16px; margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                        <div style="font-weight: 500;">时尚穿搭文案</div>
                        <div style="font-size: 12px; color: #666;">2024-01-14</div>
                    </div>
                    <div style="font-size: 14px; color: #666; margin-bottom: 8px;">行业：服装时尚 | 受众：职场白领</div>
                    <div style="background: rgba(102, 126, 234, 0.1); padding: 8px; border-radius: 6px; font-size: 14px;">职场穿搭新思路！这套搭配既优雅又不失活力...</div>
                </div>
            </div>
        </div>

        <!-- 帮助中心页面 -->
        <div class="page" id="help">
            <div class="header">
                <button class="back-btn" onclick="showPage('profile')">← 返回</button>
                <h1>帮助中心</h1>
                <div></div>
            </div>

            <div class="card">
                <h3 style="margin-bottom: 16px; color: #2c3e50;">常见问题</h3>

                <div class="grid-item" style="margin-bottom: 12px;">
                    <div>
                        <div style="font-weight: 500; margin-bottom: 8px;">如何生成高质量文案？</div>
                        <div style="font-size: 14px; color: #666;">详细填写产品卖点，选择准确的行业和受众，添加相关话题标签，这样能帮助AI生成更精准的文案。</div>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px;">
                    <div>
                        <div style="font-weight: 500; margin-bottom: 8px;">积分如何使用？</div>
                        <div style="font-size: 14px; color: #666;">每次生成文案消耗20积分，会员用户享受8折优惠。可通过充值或购买会员获得积分。</div>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px;">
                    <div>
                        <div style="font-weight: 500; margin-bottom: 8px;">如何上传和管理素材？</div>
                        <div style="font-size: 14px; color: #666;">在素材库页面点击"上传素材"，支持JPG、PNG格式图片，单张不超过5MB。</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3 style="margin-bottom: 16px; color: #2c3e50;">使用教程</h3>

                <div class="grid-item" style="margin-bottom: 12px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>新手入门指南</span>
                        <span style="color: #666;">></span>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>文案生成技巧</span>
                        <span style="color: #666;">></span>
                    </div>
                </div>

                <div class="grid-item" style="margin-bottom: 12px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>素材管理教程</span>
                        <span style="color: #666;">></span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3 style="margin-bottom: 16px; color: #2c3e50;">联系我们</h3>

                <div style="text-align: center;">
                    <div style="font-size: 14px; color: #666; margin-bottom: 12px;">技术支持微信</div>
                    <div style="width: 120px; height: 120px; background: #f0f0f0; border-radius: 8px; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">二维码</div>
                    <div style="font-size: 12px; color: #666;">扫码添加客服微信</div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="showPage('home'); setActiveNav(this)">
                <div style="font-size: 18px; margin-bottom: 4px;">🏠</div>
                <div>首页</div>
            </div>
            <div class="nav-item" onclick="showPage('history'); setActiveNav(this)">
                <div style="font-size: 18px; margin-bottom: 4px;">📝</div>
                <div>记录</div>
            </div>
            <div class="nav-item" onclick="showPage('recharge'); setActiveNav(this)">
                <div style="font-size: 18px; margin-bottom: 4px;">💎</div>
                <div>充值</div>
            </div>
            <div class="nav-item" onclick="showPage('profile'); setActiveNav(this)">
                <div style="font-size: 18px; margin-bottom: 4px;">👤</div>
                <div>我的</div>
            </div>
        </div>
    </div>

    <script>
        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
        }

        // 标签页切换功能
        function showTab(tabId) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 移除所有标签的激活状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示目标标签页内容
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // 激活对应的标签
            event.target.classList.add('active');
        }

        // 底部导航激活状态
        function setActiveNav(element) {
            // 移除所有导航项的激活状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));

            // 激活当前导航项
            element.classList.add('active');
        }

        // 模拟选择功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为网格项添加选择功能
            const gridItems = document.querySelectorAll('.grid-item');
            gridItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 如果是在受众人群或发布人身份区域
                    const parent = this.closest('.input-group');
                    if (parent) {
                        const label = parent.querySelector('label');
                        if (label && (label.textContent.includes('受众人群') || label.textContent.includes('发布人身份'))) {
                            // 切换选中状态
                            this.style.background = this.style.background === 'rgba(102, 126, 234, 0.2)' ?
                                'rgba(255, 255, 255, 0.8)' : 'rgba(102, 126, 234, 0.2)';
                            this.style.borderColor = this.style.borderColor === 'rgb(102, 126, 234)' ?
                                'rgba(0, 0, 0, 0.1)' : '#667eea';
                        }
                    }
                });
            });

            // 为充值套餐添加选择功能
            const rechargeItems = document.querySelectorAll('#recharge .grid-item');
            rechargeItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除其他项的选中状态
                    rechargeItems.forEach(otherItem => {
                        otherItem.style.borderColor = 'transparent';
                    });
                    // 选中当前项
                    this.style.borderColor = '#667eea';
                });
            });

            // 为会员套餐添加选择功能
            const membershipItems = document.querySelectorAll('#membership .grid-item');
            membershipItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除其他项的选中状态
                    membershipItems.forEach(otherItem => {
                        otherItem.style.borderColor = 'rgba(0, 0, 0, 0.1)';
                    });
                    // 选中当前项
                    this.style.borderColor = '#667eea';
                });
            });
        });

        // 模拟生成文案功能
        function generateContent() {
            alert('正在生成文案，请稍候...\n\n这是一个原型演示，实际功能需要后端支持。');
        }

        // 模拟上传素材功能
        function uploadMaterial() {
            alert('选择图片文件...\n\n这是一个原型演示，实际功能需要后端支持。');
        }

        // 为生成按钮添加事件
        document.addEventListener('DOMContentLoaded', function() {
            const generateBtn = document.querySelector('#content-gen .btn-primary');
            if (generateBtn) {
                generateBtn.addEventListener('click', generateContent);
            }
        });
    </script>
</body>
</html>