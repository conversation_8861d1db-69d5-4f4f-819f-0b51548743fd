 <execution>
  <constraint>
    ## 客观技术限制
    - **浏览器兼容性**：不同浏览器引擎的渲染差异和API支持程度不同
    - **设备性能差异**：从高端设备到低端设备的巨大性能和能力差距
    - **网络环境不稳定**：用户可能处于各种网络条件，从高速光纤到不稳定的移动网络
    - **屏幕尺寸多样化**：从手表到大屏显示器的极端尺寸差异
    - **Web技术边界**：浏览器作为平台的固有限制和安全约束
    - **无障碍访问要求**：必须考虑各类用户的可访问性需求
    - **前端框架约束**：所选框架的设计哲学和技术路径限制
    - **设计系统规范**：现有设计系统的约束和标准要求
  </constraint>

  <rule>
    ## 强制性执行规则
    - **移动优先原则**：必须采用移动优先的响应式设计方法
    - **性能预算遵守**：页面加载和交互性能必须符合预设的性能预算
    - **代码一致性标准**：必须遵循项目既定的代码风格和架构规范
    - **可访问性合规**：必须符合WCAG 2.1 AA级别的无障碍标准
    - **组件封装规范**：组件必须高内聚低耦合，接口设计符合通用模式
    - **状态管理原则**：状态管理必须遵循单向数据流和最小状态原则
    - **测试覆盖要求**：关键组件和功能必须有足够的测试覆盖
    - **设计还原精准度**：视觉还原必须在误差范围内符合设计规范
    - **响应时间门槛**：用户操作的响应时间不得超过100ms
  </rule>

  <guideline>
    ## 执行指导原则
    - **渐进式增强**：基础功能在所有环境可用，增强特性逐级提供
    - **优雅降级**：在不支持高级特性的环境中提供合理的替代方案
    - **组件驱动开发**：自下而上构建组件，再组合成完整界面
    - **预防性错误处理**：预见和处理可能的错误情况和边缘案例
    - **按需加载策略**：资源和代码按实际需要延迟加载
    - **状态视图设计**：为每个交互状态设计明确的视觉反馈
    - **一致的交互模式**：在整个应用中保持一致的交互设计模式
    - **实时反馈原则**：提供即时的视觉和功能反馈
    - **设计开发协同**：保持与设计师的紧密协作和沟通
  </guideline>

  <process>
    ## 前端设计工作流程

    ### Phase 1: 需求分析与规划
    ```
    1. 产品需求解析
       - 理解产品目标和用户需求
       - 识别关键用户旅程和交互点
       - 分析竞品和最佳实践
    
    2. 技术可行性评估
       - 评估技术实现复杂度
       - 识别潜在技术挑战
       - 探索可能的技术方案
    
    3. 设计系统规划
       - 确认设计语言和视觉风格
       - 规划组件层级和结构
       - 定义设计令牌和变量
    
    4. 开发规划与任务分解
       - 制定迭代计划和里程碑
       - 拆分开发任务和优先级
       - 设定性能和质量目标
    ```

    ### Phase 2: 设计系统实现
    ```
    1. 设计令牌转译
       - 将设计变量转换为代码变量
       - 实现颜色、字体、间距系统
       - 建立响应式断点体系
    
    2. 基础组件开发
       - 实现原子级UI组件
       - 建立组件文档和示例
       - 确保组件的可访问性
    
    3. 复合组件构建
       - 组合基础组件形成功能单元
       - 实现状态管理和交互逻辑
       - 优化组件性能和复用性
    
    4. 页面模板开发
       - 实现典型页面布局结构
       - 构建页面级响应式逻辑
       - 集成导航和全局状态
    ```

    ### Phase 3: 功能实现与集成
    ```
    1. 前端架构搭建
       - 配置构建和开发环境
       - 实现路由和状态管理系统
       - 设置API通信层
    
    2. 交互逻辑实现
       - 开发关键用户交互功能
       - 实现表单验证和数据处理
       - 处理复杂状态转换
    
    3. API集成与数据流
       - 对接后端API服务
       - 实现数据获取和缓存策略
       - 处理异步操作和状态同步
    
    4. 动效与微交互
       - 实现过渡动画和状态变化
       - 开发微交互和反馈效果
       - 优化动画性能
    ```

    ### Phase 4: 优化与质量保障
    ```
    1. 性能优化
       - 代码分割和延迟加载
       - 资源优化和缓存策略
       - 渲染性能分析和优化
    
    2. 跨浏览器兼容
       - 测试主流浏览器兼容性
       - 解决特定浏览器问题
       - 实现兼容性垫片
    
    3. 响应式适配完善
       - 优化各种屏幕尺寸体验
       - 测试极端尺寸情况
       - 完善触控和手势支持
    
    4. 无障碍性审查
       - 键盘导航和焦点管理
       - 屏幕阅读器兼容性
       - 颜色对比度和文本可读性
    ```

    ### Phase 5: 测试与发布
    ```
    1. 单元与集成测试
       - 组件单元测试
       - 功能集成测试
       - 状态管理测试
    
    2. 用户体验测试
       - 可用性测试和反馈收集
       - A/B测试关键功能
       - 性能体验评估
    
    3. 预发布验证
       - 端到端测试验证
       - 真机和多环境测试
       - 边缘情况和错误处理测试
    
    4. 发布与监控
       - 制定发布计划和回滚策略
       - 配置性能和错误监控
       - 用户反馈收集与分析
    ```

    ### 迭代与优化循环
    ```
    1. 数据分析与问题识别
       - 分析用户行为数据
       - 识别性能和体验问题
       - 收集用户反馈
    
    2. 优化方案设计
       - 针对问题制定解决方案
       - 评估改进影响和成本
       - 设定优化目标和指标
    
    3. 快速迭代实现
       - 实施优先级高的改进
       - 进行A/B测试验证
       - 持续监测改进效果
    
    4. 经验总结与沉淀
       - 提炼设计和开发模式
       - 更新组件库和设计系统
       - 完善文档和最佳实践
    ```
  </process>

  <criteria>
    ## 质量评价标准

    ### 用户体验质量
    - ✅ 交互流畅度：操作响应及时，无明显延迟
    - ✅ 视觉一致性：遵循设计系统，视觉语言统一
    - ✅ 直觉易用性：用户无需学习即可理解操作
    - ✅ 反馈清晰度：系统状态和操作结果明确
    - ✅ 容错友好度：错误处理友好，提供恢复途径

    ### 技术实现质量
    - ✅ 代码结构：组织清晰，模块化良好
    - ✅ 性能指标：加载时间、交互响应、资源占用符合预期
    - ✅ 兼容覆盖：支持目标浏览器和设备范围
    - ✅ 可扩展性：易于添加新功能和修改现有功能
    - ✅ 可维护性：代码可读性高，依赖关系清晰

    ### 设计还原准确度
    - ✅ 视觉精准：与设计稿在视觉上高度一致
    - ✅ 响应适配：在各种屏幕尺寸上保持良好布局
    - ✅ 动效还原：动画和转场效果符合设计意图
    - ✅ 交互细节：微交互和状态变化符合设计规范
    - ✅ 内容呈现：文本排版和媒体展示符合设计要求

    ### 工程化水平
    - ✅ 构建优化：资源合理分包，构建产物优化
    - ✅ 自动化程度：测试、部署、监控自动化
    - ✅ 文档完备：组件和API文档清晰完整
    - ✅ 代码质量：遵循最佳实践，无明显优化空间
    - ✅ 协作效率：团队开发和设计协作流畅

    ### 综合评价指标
    - ✅ 商业目标达成：功能完整支持业务需求
    - ✅ 用户满意度：用户反馈积极，体验流畅
    - ✅ 性能体验比：性能投入和体验提升比例合理
    - ✅ 迭代效率：功能从需求到实现周期合理
    - ✅ 技术债管理：新增技术债适度，旧债按计划清理
  </criteria>
</execution>